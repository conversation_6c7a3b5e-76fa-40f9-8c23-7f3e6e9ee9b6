#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工具
验证自动注册工具的基本功能
"""

import configparser
import pyautogui
import pyperclip
import time
import sys
import os

def test_config_file():
    """测试配置文件是否存在且格式正确"""
    print("测试配置文件...")
    
    if not os.path.exists('config.ini'):
        print("❌ 配置文件 config.ini 不存在")
        return False
    
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        # 检查必要的配置节
        required_sections = ['BROWSER', 'EMAIL_APP', 'VERIFICATION_APP', 'REGISTRATION', 'DELAYS']
        for section in required_sections:
            if not config.has_section(section):
                print(f"❌ 配置文件缺少 [{section}] 节")
                return False
        
        print("✅ 配置文件格式正确")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def test_dependencies():
    """测试依赖包是否正确安装"""
    print("测试依赖包...")
    
    required_packages = [
        'selenium',
        'webdriver_manager',
        'pyautogui',
        'pyperclip',
        'requests',
        'bs4',  # beautifulsoup4 的导入名称是 bs4
        'PIL',
        'cv2',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def test_pyautogui():
    """测试pyautogui功能"""
    print("测试pyautogui功能...")
    
    try:
        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        print(f"✅ 屏幕尺寸: {screen_width} x {screen_height}")
        
        # 获取鼠标位置
        x, y = pyautogui.position()
        print(f"✅ 当前鼠标位置: ({x}, {y})")
        
        return True
        
    except Exception as e:
        print(f"❌ pyautogui测试失败: {e}")
        return False

def test_clipboard():
    """测试剪贴板功能"""
    print("测试剪贴板功能...")
    
    try:
        # 测试写入剪贴板
        test_text = "测试文本_" + str(int(time.time()))
        pyperclip.copy(test_text)
        
        # 测试读取剪贴板
        clipboard_content = pyperclip.paste()
        
        if clipboard_content == test_text:
            print("✅ 剪贴板功能正常")
            return True
        else:
            print(f"❌ 剪贴板测试失败: 期望 '{test_text}', 实际 '{clipboard_content}'")
            return False
            
    except Exception as e:
        print(f"❌ 剪贴板测试失败: {e}")
        return False

def test_browser_drivers():
    """测试浏览器驱动"""
    print("测试浏览器驱动...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from webdriver_manager.firefox import GeckoDriverManager
        from webdriver_manager.microsoft import EdgeChromiumDriverManager
        
        print("✅ 浏览器驱动管理器导入成功")
        
        # 注意：这里不实际下载驱动，只是测试导入
        print("ℹ️  首次运行时会自动下载浏览器驱动")
        return True
        
    except Exception as e:
        print(f"❌ 浏览器驱动测试失败: {e}")
        return False

def test_coordinate_capture():
    """测试坐标捕获功能"""
    print("测试坐标捕获功能...")
    
    try:
        print("将在3秒后捕获当前鼠标坐标...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        x, y = pyautogui.position()
        print(f"✅ 捕获坐标: ({x}, {y})")
        return True
        
    except Exception as e:
        print(f"❌ 坐标捕获失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("自动注册工具 - 功能测试")
    print("=" * 50)
    print()
    
    tests = [
        ("配置文件", test_config_file),
        ("依赖包", test_dependencies),
        ("PyAutoGUI", test_pyautogui),
        ("剪贴板", test_clipboard),
        ("浏览器驱动", test_browser_drivers),
        ("坐标捕获", test_coordinate_capture)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name}测试 ---")
        if test_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具可以正常使用。")
        print("\n下一步:")
        print("1. 运行 python simple_coordinate_helper.py 获取坐标")
        print("2. 修改 config.ini 配置文件")
        print("3. 运行 python auto_register.py 开始自动注册")
    else:
        print("⚠️  部分测试失败，请解决问题后重新测试。")
        
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n\n测试程序异常: {e}")
    
    input("\n按回车键退出...")
